import React, { useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { FlatList, I18nManager, StyleSheet, Text, TouchableOpacity } from 'react-native';
import { Row } from 'react-native-col';
import { useTranslation } from 'react-i18next';
import { NewProductsLoader } from '../../loader';
import { CardSecondaryOffline } from '../../product-cards';
import { colors, fonts } from '../../../utils/theme';
import { useAppSelector } from '../../../redux/hooks';
import { ArrowRightLong } from '../../../assets/svgs/icons';
import { getPriceListObject } from '../../../utils/functions';
import { setProductListPrice, setSelectedProduct } from '../../../redux/features/productDetails-slice';
import { getPriceListId, getRestockedProductsForTablet } from '../../../redux/selectors';
import { HORIZONTAL_DIMENS } from '../../../constants';

type FlastListItem = {
	item: any;
	index: number;
};

// const estimatedItemSize = Math.floor(HORIZONTAL_DIMENS._187 + 80);
/* -------------------------------------------------------------------------- */
/*                        Used in offline(tablet) only.                       */
/* -------------------------------------------------------------------------- */
const RestockedProductsOffline = () => {
	const { t } = useTranslation();
	const dispatch = useDispatch();
	const navigation = useNavigation<NativeStackNavigationProp<any>>();
	const priceListId = useAppSelector(getPriceListId);
	// const { loadingNewProducts } = useAppSelector(state => state.home);
	const newProducts = useAppSelector(getRestockedProductsForTablet);
	const loadingData = useAppSelector(state => state.auth.loadingData);
	newProducts.length = newProducts.length > 10 ? 10 : newProducts.length;

	const openProductDetails = (product: any) => {
		const parsedProduct = JSON.parse(JSON.stringify(product));
		const finalPriceListItem = getPriceListObject(product.price_mappings, priceListId);
		dispatch(setProductListPrice(finalPriceListItem));
		dispatch(setSelectedProduct(parsedProduct._id));
		navigation.navigate('CatalogVariant', { productId: parsedProduct._id });
	};

	const renderProductCard = useCallback(({ item, index }: FlastListItem) => {
		//console.log('item in new products', item);

		const isEnd = index === newProducts.length - 1;
		return (
			<CardSecondaryOffline
				item={item}
				containerStyle={isEnd && styles.productLastItem}
				onPress={openProductDetails}
			/>
		);
	}, [priceListId, newProducts]);

	const viewAll = () => {
		navigation.navigate('RestockedProducts');
	};

	const keyExtractor = useCallback((item: any, index: number) => `${item._id}-${index}`, []);

	if (loadingData) {
		return <NewProductsLoader />;
	}

	return (
		<>
			{newProducts.length > 0 && (
				<>
					<Row.LR>
						<Text style={styles.listHeader}>{t('restock_products')}</Text>
						<TouchableOpacity style={styles.viewAll} onPress={viewAll}>
							<Text style={styles.viewAllText}>{t('view_all')}</Text>
							<ArrowRightLong fill={colors.secondary} style={{ transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] }} />
						</TouchableOpacity>
					</Row.LR>
					<FlatList
						horizontal
						data={newProducts}
						// extraData={allProducts.length}
						// estimatedItemSize={estimatedItemSize}
						renderItem={renderProductCard}
						keyExtractor={keyExtractor}
						contentContainerStyle={styles.listContainer}
						showsHorizontalScrollIndicator={false}
					/>
				</>
			)}
		</>
	);
};

const styles = StyleSheet.create({
	listContainer: {
		//flex: 1,
		paddingLeft: 30,
		paddingBottom: 13
	},
	listHeader: {
		marginLeft: 30,
		paddingVertical: 28,
		fontFamily: fonts.Montserrat.SemiBold,
		color: colors.primary,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._20,
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	viewAll: {
		alignItems: 'center',
		flexDirection: 'row',
		marginRight: 30
	},
	viewAllText: {
		color: colors.secondary,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._14,
		marginRight: 12,
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	productLastItem: {
		marginRight: 30
	}
});

export { RestockedProductsOffline };