import { Dimensions, StyleSheet } from 'react-native';
import { HORIZONTAL_DIMENS, VERTICAL_DIMENS } from '../../constants';
import { colors, fonts } from '../../utils/theme';

const width = Dimensions.get('window').width;

export default StyleSheet.create({
	safeAreaView: {
		flex: 1,
		backgroundColor: colors.grey100
	},
	heading: {
		marginTop: VERTICAL_DIMENS._13,
		color: colors.primary,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._18
	},
	loadingContainer: {
		flex: 1,
		alignItems: 'center',
		justifyContent: 'center'
	},
	listContainer: {
		flexGrow: 1,
		marginTop: VERTICAL_DIMENS._6,
		paddingHorizontal: 16,
		paddingBottom: VERTICAL_DIMENS._30
	},
	noResults: {
		marginTop: VERTICAL_DIMENS._13,
		color: colors.grey400,
		fontFamily: fonts.Montserrat.Regular,
		fontWeight: '400',
		fontSize: HORIZONTAL_DIMENS._14,
		textAlign: 'center'
	},
	cardContainer: {
		marginTop: VERTICAL_DIMENS._13,
		marginRight: HORIZONTAL_DIMENS._8,
		width: (width - 40) / 2
	},
	footerLoader: {
		height: 60,
		justifyContent: 'center',
		alignItems: 'center'
	}
});
