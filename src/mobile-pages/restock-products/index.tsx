import React, { useCallback, useState } from 'react';
import {
	View,
	Text,
	FlatList,
	ActivityIndicator,
	StatusBar,
	RefreshControl
} from 'react-native';
import { useDispatch } from 'react-redux';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';
import { PrimaryCardMobile } from '../../components/product-cards';
import { useAppSelector } from '../../redux/hooks';
import { getRestockedProducts } from '../../redux/apis/product';
import { getBranchId, getPriceListId, getSalesPersonRoleId, getUserSetting, getUserType } from '../../redux/selectors';
import { setProductListPrice, setSelectedGroup, setSelectedProduct, setSelectedvariant } from '../../redux/features/productDetails-slice';
import { colors } from '../../utils/theme';
import { SEARCH_TYPES, USER_TYPES } from '../../constants';
import { getPriceListObject } from '../../utils/functions';
import styles from './styles';

const RestockProductsScreen = () => {
	const { t, i18n } = useTranslation();
	const dispatch = useDispatch();
	const navigation = useNavigation<any>();
	const userType = useAppSelector(getUserType);
	const branchId = useAppSelector(getBranchId);
	const priceListId = useAppSelector(getPriceListId);
	const salesPersonRoleId = useAppSelector(getSalesPersonRoleId);
	const userSetting = useAppSelector(getUserSetting);
	const currentRole = useAppSelector(state => state.auth.currentRole);
	const { restockedProducts, restockedProductsCount } = useAppSelector(state => state.home);
	const [page, setPage] = useState(2);
	const [isLoading, setIsLoading] = useState(false);
	const [isFetchingNext, setIsFetchingNext] = useState(false);
	const [refreshing, setRefreshing] = useState(false);

	// Load fresh data when user navigates to this page
	useFocusEffect(
		useCallback(() => {
			if (priceListId) {
				searchProducts(true); // Load fresh data (page 1)
			}
		}, [priceListId])
	);

	const searchProducts = async (isCleanFetch: boolean, isRefresh: boolean = false) => {
		const setLoading = isCleanFetch ? setIsLoading : setIsFetchingNext;
		if (!isRefresh) {
			setLoading(true);
		}

		const currentPage = isCleanFetch || isRefresh ? 1 : page;
		let requestBody: any = {
			priceListId,
			tenantId: currentRole?.tenant_id?._id,
			page: currentPage,
			perPage: 10,
			searchType: SEARCH_TYPES.FILTER,
			isPrimaryLanguage: i18n.language === 'en',
			filters: { productType: ['RESTOCKED_PRODUCTS'] },
			hideOutOfStock: userType === USER_TYPES.CUSTOMER_APP ? true : !userSetting?.out_of_stock?.visible,
			branchId: branchId
		};
		if (salesPersonRoleId) {
			requestBody.salesPersonUserRoleId = salesPersonRoleId;
		}

		await dispatch(getRestockedProducts(requestBody));

		if (isCleanFetch || isRefresh) {
			setPage(2);
		} else {
			setPage(page + 1);
		}

		if (!isRefresh) {
			setLoading(false);
		}
	};

	const onRefresh = useCallback(async () => {
		setRefreshing(true);
		// No need to clear products manually - Redux slice handles page 1 replacement
		await searchProducts(true, true);
		setRefreshing(false);
	}, [priceListId, salesPersonRoleId, userSetting?.out_of_stock, branchId, currentRole?.tenant_id?._id, i18n.language, userType]);

	const openCatalogVariant = (item: any) => {
		const finalPriceListItem = getPriceListObject(item.price_mappings, priceListId);
		dispatch(setSelectedProduct(item._id));
		if (item?.group_value_id) {
			dispatch(setSelectedGroup(item?.group_value_id?._id));
			dispatch(setSelectedvariant(item?.variant_value_id));
		}
		dispatch(setProductListPrice(finalPriceListItem));
		const itemId = item?._id;
		navigation.navigate('CatalogVariant', { itemId });
	};

	const onEndReached = useCallback(() => {
		if (!isFetchingNext) {
			if (restockedProducts.length < restockedProductsCount) {
				searchProducts(false);
			}
		}
	}, [isFetchingNext, restockedProducts.length, restockedProductsCount]);

	const renderProductItem = ({ item, index }: any) => {
		return (
			<PrimaryCardMobile
				key={`${item.id}-${index}`}
				item={item}
				containerStyle={styles.cardContainer}
				onPress={() => openCatalogVariant(item)}
			/>
		);
	};

	const keyExtractor = useCallback((item: any, index: number) => `product-${item._id}-${index}`, []);

	return (
		<View style={styles.safeAreaView}>
			<StatusBar barStyle={'light-content'} />
			{
				isLoading ? (
					<View style={styles.loadingContainer}>
						<ActivityIndicator color={colors.grey600} size={'large'} />
					</View>
				) : (
					<FlatList
						contentContainerStyle={styles.listContainer}
						showsVerticalScrollIndicator={false}
						data={restockedProducts}
						renderItem={renderProductItem}
						numColumns={2}
						windowSize={10}
						onEndReachedThreshold={0.2}
						onEndReached={onEndReached}
						keyExtractor={keyExtractor}
						ListEmptyComponent={<Text style={styles.noResults}>{t('no_result_found')}</Text>}
						keyboardShouldPersistTaps='handled'
						refreshControl={
							<RefreshControl
								refreshing={refreshing}
								onRefresh={onRefresh}
								tintColor={colors.grey400}
							/>
						}
						ListFooterComponent={isFetchingNext ? (
							<View style={styles.footerLoader} >
								<ActivityIndicator size={'small'} />
							</View>
						) : null}
					/>
				)
			}
		</View>
	);
};

export default RestockProductsScreen;
