import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Text, TouchableOpacity, View, StyleSheet, ActivityIndicator } from 'react-native';
import { useDispatch } from 'react-redux';
import { CheckBox, PrimaryButton } from '../../components/common';
import { HORIZONTAL_DIMENS, VERTICAL_DIMENS } from '../../constants';
import { colors, fonts } from '../../utils/theme';
import { useAppSelector } from '../../redux/hooks';
import { getDefaultCustomerType, getLanguages, getUserSetting } from '../../redux/selectors';
import { changeAppLanguage, needsRTLChange } from '../../utils/languageUtils';
import { updateUserSetting, getUserSetting as getUserSettingFromServer } from '../../redux/apis/sttings';
import { useFocusEffect } from '@react-navigation/native';

const Language = () => {
	const { t, i18n } = useTranslation();
	const dispatch = useDispatch();
	const [appLanguage, setAppLanguage] = useState(i18n.language);
	const [communicationLanguage, setCommunicationLanguage] = useState(i18n.language);
	const [isLoading, setIsLoading] = useState(false);
	const [communicationLoading, setCommunicationLoading] = useState(false);
	const languages = useAppSelector(getLanguages);
	const defaultCustomerType = useAppSelector(getDefaultCustomerType);
	const userSetting = useAppSelector(getUserSetting);
	const currentRole = useAppSelector(state => state.auth.currentRole);

	useEffect(() => {
		setCommunicationLanguage(userSetting.preferred_language || i18n.language);
	}, [userSetting, i18n.language]);

	useFocusEffect(
		React.useCallback(() => {
			// Fetch user settings when the screen is focused
			const fetchUserSettings = async () => {
				await dispatch(getUserSettingFromServer());
			};
			fetchUserSettings();
		}, [dispatch])
	);

	const handleAppLanguage = async () => {
		try {
			setIsLoading(true);

			// Check if RTL change is needed to show appropriate message
			const willRestart = needsRTLChange(appLanguage);

			if (willRestart) {
				// Show message that app will restart
				console.log('App will restart to apply language changes');
			}

			// Change the app language and handle RTL
			await changeAppLanguage(appLanguage);

			// If we reach here, no restart was needed
			setIsLoading(false);
		} catch (error) {
			console.error('Error changing language:', error);
			setIsLoading(false);
		}
	};

	const handleCommunicationLanguage = async () => {
		setCommunicationLoading(true);
		const requestBody: any = {
			masterPriceId: userSetting.default_master_price_id ? userSetting.default_master_price_id : defaultCustomerType.value,
			out_of_stock: {
				visible: userSetting.out_of_stock.visible,
				searchable: userSetting.out_of_stock.searchable
			},
			priceChange: userSetting.price_change,
			preferredLanguage: communicationLanguage,
			tenantId: currentRole?.tenant_id?._id
		};

		// Update the user setting in the backend
		await dispatch(updateUserSetting(requestBody));
		await dispatch(getUserSettingFromServer());
		setCommunicationLoading(false);
	};

	return (
		<View style={styles.mainContainer}>
			{/* Loading Overlay */}
			{(isLoading || communicationLoading) && (
				<View style={styles.loadingOverlay}>
					<View style={styles.loadingContainer}>
						<ActivityIndicator size="large" color={colors.primary} />
						<Text style={styles.loadingText}>
							{isLoading
								? (needsRTLChange(appLanguage)
									? t('changing_language_restart', 'Changing language, app will restart...')
									: t('changing_language', 'Changing language...'))
								: t('updating_communication_language', 'Updating communication language...')
							}
						</Text>
					</View>
				</View>
			)}

			{/* App Language Section */}
			<View style={styles.sectionContainer}>
				<Text style={styles.sectionTitle}>{t('app_language')}</Text>
				<View style={[
					styles.languageSection,
					(isLoading || communicationLoading) && styles.disabledContainer
				]}>
					{
						languages.map((item, index) => {
							const isLast = languages.length - 1 === index;
							return (
								<TouchableOpacity
									style={[styles.languageContainer, !isLast && styles.itemBottomBorder]}
									onPress={() => !(isLoading || communicationLoading) && setAppLanguage(item.value)}
									key={`app-${item.value}`}
									disabled={isLoading || communicationLoading}
								>
									<Text style={[
										styles.languageContentTitle,
										(isLoading || communicationLoading) && styles.disabledText
									]}>
										{item.name}
									</Text>
									<CheckBox
										checked={appLanguage === item.value}
										onChange={() => !(isLoading || communicationLoading) && setAppLanguage(item.value)}
									/>
								</TouchableOpacity>
							);
						})
					}
				</View>
				<View style={styles.buttonContainer}>
					<PrimaryButton
						title={t('confirm')}
						titleStyle={styles.titleStyle}
						onPress={handleAppLanguage}
						style={[styles.confirmButton, (isLoading || communicationLoading) && styles.disabledButton]}
						disabled={isLoading || communicationLoading}
						loading={isLoading}
					/>
				</View>
			</View>

			{/* Communication Language Section */}
			<View style={styles.sectionContainer}>
				<Text style={styles.sectionTitle}>{t('communication_language')}</Text>
				<View style={[
					styles.languageSection,
					(isLoading || communicationLoading) && styles.disabledContainer
				]}>
					{
						languages.map((item, index) => {
							const isLast = languages.length - 1 === index;
							return (
								<TouchableOpacity
									style={[styles.languageContainer, !isLast && styles.itemBottomBorder]}
									onPress={() => !(isLoading || communicationLoading) && setCommunicationLanguage(item.value)}
									key={`comm-${item.value}`}
									disabled={isLoading || communicationLoading}
								>
									<Text style={[
										styles.languageContentTitle,
										(isLoading || communicationLoading) && styles.disabledText
									]}>
										{item.name}
									</Text>
									<CheckBox
										checked={communicationLanguage === item.value}
										onChange={() => !(isLoading || communicationLoading) && setCommunicationLanguage(item.value)}
									/>
								</TouchableOpacity>
							);
						})
					}
				</View>
				<View style={styles.buttonContainer}>
					<PrimaryButton
						title={t('confirm')}
						titleStyle={styles.titleStyle}
						onPress={handleCommunicationLanguage}
						style={[styles.confirmButton, (isLoading || communicationLoading) && styles.disabledButton]}
						disabled={isLoading || communicationLoading}
						loading={communicationLoading}
					/>
				</View>
			</View>
		</View>
	);
};
export default Language;

const styles = StyleSheet.create({
	mainContainer: {
		flex: 1
	},
	sectionContainer: {
		marginBottom: VERTICAL_DIMENS._24
	},
	sectionTitle: {
		fontFamily: fonts.Montserrat.SemiBold,
		fontSize: HORIZONTAL_DIMENS._18,
		fontWeight: '600',
		color: colors.primary,
		marginHorizontal: HORIZONTAL_DIMENS._16,
		marginTop: VERTICAL_DIMENS._16,
		marginBottom: VERTICAL_DIMENS._12
	},
	languageSection: {
		backgroundColor: colors.white
	},
	languageContainer: {
		paddingVertical: VERTICAL_DIMENS._14,
		flexDirection: 'row',
		marginHorizontal: HORIZONTAL_DIMENS._16,
		justifyContent: 'space-between'
	},
	itemBottomBorder: {
		borderBottomColor: colors.grey200,
		borderBottomWidth: 1
	},
	languageContentTitle: {
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._16,
		fontWeight: '400',
		color: colors.primary
	},
	confirmButton: {
		marginHorizontal: HORIZONTAL_DIMENS._40
	},
	buttonContainer: {
		marginTop: VERTICAL_DIMENS._16,
		width: '100%'
	},
	titleStyle: {
		textTransform: 'uppercase'
	},
	// Loading styles
	loadingOverlay: {
		position: 'absolute',
		top: 0,
		left: 0,
		right: 0,
		bottom: 0,
		backgroundColor: 'rgba(0, 0, 0, 0.5)',
		justifyContent: 'center',
		alignItems: 'center',
		zIndex: 1000
	},
	loadingContainer: {
		backgroundColor: colors.white,
		padding: HORIZONTAL_DIMENS._24,
		borderRadius: HORIZONTAL_DIMENS._12,
		alignItems: 'center',
		minWidth: 200
	},
	loadingText: {
		marginTop: VERTICAL_DIMENS._16,
		fontFamily: fonts.Montserrat.Medium,
		fontSize: HORIZONTAL_DIMENS._16,
		color: colors.primary,
		textAlign: 'center'
	},
	disabledContainer: {
		opacity: 0.6
	},
	disabledText: {
		color: colors.grey400
	},
	disabledButton: {
		opacity: 0.6
	}
});