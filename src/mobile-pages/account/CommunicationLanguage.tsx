import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Text, TouchableOpacity, View, StyleSheet } from 'react-native';
import { useDispatch } from 'react-redux';
import { CheckBox, PrimaryButton } from '../../components/common';
import { HORIZONTAL_DIMENS, VERTICAL_DIMENS } from '../../constants';
import { colors, fonts } from '../../utils/theme';
import { useAppSelector } from '../../redux/hooks';
import { getDefaultCustomerType, getLanguages, getUserSetting } from '../../redux/selectors';
import { updateUserSetting } from '../../redux/apis/sttings';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { getUserSetting as getUserSettingFromServer } from '../../redux/apis/sttings';
const CommunicationLanguage = () => {
	const { t, i18n } = useTranslation();
	const dispatch = useDispatch();
	const navigation = useNavigation<any>();
	const [language, setLanguage] = useState(i18n.language);
	const languages = useAppSelector(getLanguages);
	const defaultCustomerType = useAppSelector(getDefaultCustomerType);
	const userSetting = useAppSelector(getUserSetting);
	const [loading, setloading] = useState(false);
	console.log('🚀 ~ Language ~ userSetting:', JSON.stringify(userSetting));
	const currentRole = useAppSelector(state => state.auth.currentRole);
	useEffect(() => {
		setLanguage(userSetting.preferred_language || i18n.language);
	}, [userSetting]);
	useFocusEffect(
		React.useCallback(() => {
			// Fetch user settings when the screen is focused
			const fetchUserSettings = async () => {
				await dispatch(getUserSettingFromServer());
			};
			fetchUserSettings();
		}, [dispatch])
	);
	const handleLanguage = async () => {
		setloading(true);
		const requestBody: any = {
			masterPriceId: userSetting.default_master_price_id ? userSetting.default_master_price_id : defaultCustomerType.value,
			out_of_stock: {
				visible: userSetting.out_of_stock.visible,
				searchable: userSetting.out_of_stock.searchable
			},
			priceChange: userSetting.price_change,
			preferredLanguage: language,
			tenantId: currentRole?.tenant_id?._id
		};

		// First update the user setting in the backend
		await dispatch(updateUserSetting(requestBody));
		await dispatch(getUserSettingFromServer());
		setloading(false);
		// Navigate back to the previous screen
		navigation.goBack();
		// Then change the app language and handle RTL
		// await changeAppLanguage(language);
	};

	return (
		<View style={styles.mainContainer}>
			<View style={{ marginTop: VERTICAL_DIMENS._16, backgroundColor: colors.white }}>
				{
					languages.map((item, index) => {
						const isLast = languages.length - 1 === index;
						return (
							<TouchableOpacity
								style={[styles.languageContainer, !isLast && styles.itemBottomBorder]}
								onPress={() => setLanguage(item.value)}
								key={item.value}
							>
								<Text style={styles.languageContentTitle}>{item.name}</Text>
								<CheckBox
									checked={language === item.value}
									onChange={() => setLanguage(item.value)}
								/>
							</TouchableOpacity>
						);
					})
				}
			</View>
			<View style={styles.buttonContainer}>
				<PrimaryButton
					title={t('confirm')}
					titleStyle={styles.titleStyle}
					onPress={handleLanguage}
					style={styles.confirmButton}
					loading={loading}
				/>
			</View>
		</View>

	);
};
export default CommunicationLanguage;

const styles = StyleSheet.create({
	mainContainer: {
		flex: 1
	},
	languageContainer: {
		paddingVertical: VERTICAL_DIMENS._14,
		flexDirection: 'row',
		marginHorizontal: HORIZONTAL_DIMENS._16,
		justifyContent: 'space-between'
	},
	itemBottomBorder: {
		borderBottomColor: colors.grey200,
		borderBottomWidth: 1
	},
	languageContentTitle: {
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._16,
		fontWeight: '400',
		color: colors.primary
	},
	confirmButton: {
		marginHorizontal: HORIZONTAL_DIMENS._40
	},
	buttonContainer: {
		position: 'absolute',
		width: '100%',
		bottom: VERTICAL_DIMENS._34
	},
	titleStyle: {
		textTransform: 'uppercase'
	}
});