/**
 * Notification Debug Utilities
 * 
 * This file contains utility functions to help debug notification issues
 * including timing conflicts and cleanup problems.
 */

import NotificationService from '../services/NotificationService';
import notifee from '@notifee/react-native';

/**
 * Test notification scheduling with multiple shifts
 * This helps identify timing conflicts between clock-in and clock-out reminders
 */
export const testNotificationScheduling = async () => {
	console.log('🧪 Testing notification scheduling...');

	// Mock shift data similar to what the app uses
	const mockShifts = [
		{
			shift_name: 'Morning Shift',
			shift_start: '09:00',
			shift_end: '17:00'
		},
		{
			shift_name: 'Evening Shift',
			shift_start: '18:00',
			shift_end: '02:00'
		}
	];

	const mockReminder = {
		clock_in_before: 15, // 15 minutes before
		clock_out_after: 0   // At clock-out time
	};

	try {
		// Clear existing notifications first
		await NotificationService.removeAllReminderNotifications();

		// Schedule clock-in reminders
		if (mockReminder.clock_in_before) {
			console.log('⏰ Scheduling clock-in reminders...');
			for (let index = 0; index < mockShifts.length; index++) {
				const shift = mockShifts[index];
				const notificationId = `100${index + 1}`;

				await NotificationService.createReminderNotification({
					shiftName: shift.shift_name,
					shiftTime: shift.shift_start,
					clockMinutes: mockReminder.clock_in_before,
					clockType: 'clock_in',
					notificationId
				});
			}
		}

		// Schedule clock-out reminders
		if (mockReminder.clock_out_after !== undefined) {
			console.log('⏰ Scheduling clock-out reminders...');
			for (let index = 0; index < mockShifts.length; index++) {
				const shift = mockShifts[index];
				const notificationId = `200${index + 1}`;

				await NotificationService.createReminderNotification({
					shiftName: shift.shift_name,
					shiftTime: shift.shift_end,
					clockMinutes: mockReminder.clock_out_after,
					clockType: 'clock_out',
					notificationId
				});
			}
		}

		// List all scheduled notifications
		console.log('📋 Listing all scheduled notifications after creation:');
		await NotificationService.listScheduledNotifications();

	} catch (error) {
		console.error('❌ Error in test notification scheduling:', error);
	}
};

/**
 * Test notification cleanup functionality
 */
export const testNotificationCleanup = async () => {
	console.log('🧪 Testing notification cleanup...');

	try {
		// First schedule some test notifications
		await testNotificationScheduling();

		console.log('🗑️ Testing clock-in notification removal...');
		await NotificationService.removeClockInNotifications();

		console.log('📋 Notifications after clock-in removal:');
		await NotificationService.listScheduledNotifications();

		console.log('🗑️ Testing clock-out notification removal...');
		await NotificationService.removeClockOutNotifications();

		console.log('📋 Notifications after clock-out removal:');
		await NotificationService.listScheduledNotifications();

		console.log('🗑️ Testing complete cleanup...');
		await NotificationService.clearNotificationsOnLogout();

		console.log('📋 Notifications after complete cleanup:');
		await NotificationService.listScheduledNotifications();

	} catch (error) {
		console.error('❌ Error in test notification cleanup:', error);
	}
};

/**
 * Analyze notification timing conflicts
 * This function helps identify if notifications are scheduled at the same time
 */
export const analyzeNotificationTiming = async () => {
	console.log('🔍 Analyzing notification timing...');

	try {
		const triggerNotifications = await notifee.getTriggerNotifications();

		if (triggerNotifications.length === 0) {
			console.log('ℹ️ No scheduled notifications found');
			return;
		}

		// Group notifications by timestamp to find conflicts
		const timeGroups: { [key: string]: any[] } = {};

		triggerNotifications.forEach((notification: any) => {
			const trigger = notification.trigger as any;
			const timestamp = trigger.timestamp;
			const timeKey = new Date(timestamp).toISOString();

			if (!timeGroups[timeKey]) {
				timeGroups[timeKey] = [];
			}

			timeGroups[timeKey].push({
				id: notification.notification.id,
				title: notification.notification.title,
				body: notification.notification.body
			});
		});

		// Check for timing conflicts
		const conflicts = Object.entries(timeGroups).filter(([_, notifications]) => notifications.length > 1);

		if (conflicts.length > 0) {
			console.log('⚠️ Found timing conflicts:');
			conflicts.forEach(([time, notifications]) => {
				console.log(`🕐 Time: ${new Date(time).toLocaleString()}`);
				notifications.forEach(notification => {
					console.log(`  - ID: ${notification.id}, Title: ${notification.title}`);
				});
			});
		} else {
			console.log('✅ No timing conflicts found');
		}

		// Show all scheduled times
		console.log('📅 All scheduled notification times:');
		Object.entries(timeGroups).forEach(([time, notifications]) => {
			console.log(`🕐 ${new Date(time).toLocaleString()}: ${notifications.length} notification(s)`);
		});

	} catch (error) {
		console.error('❌ Error analyzing notification timing:', error);
	}
};


