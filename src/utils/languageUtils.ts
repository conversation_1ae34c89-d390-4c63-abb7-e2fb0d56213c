import { I18nManager } from 'react-native';
import RNRestart from 'react-native-restart';
import i18n from '../locales/i18n';
import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Changes the app language and handles RTL direction
 * @param language The language code ('en' or 'ar')
 * @returns Promise that resolves when language is changed
 */
export const changeAppLanguage = async (language: string): Promise<void> => {
	try {
		console.log('Changing language to:', language);

		// Set language in i18next
		await i18n.changeLanguage(language);

		// Cache the language
		await AsyncStorage.setItem('USER_LANG', language);

		// Handle RTL
		const isLangRTL = language === 'ar';
		const rtlChanged = isLangRTL !== I18nManager.isRTL;

		// Only update RTL settings and restart if needed
		if (rtlChanged) {
			console.log('RTL setting needs to change. Current:', I18nManager.isRTL, 'New:', isLangRTL);

			// Force the correct RTL setting
			I18nManager.allowRTL(isLangRTL);
			I18nManager.forceRTL(isLangRTL);

			// Restart the app to apply RTL changes
			console.log('Restarting app to apply RTL changes');
			RNRestart.Restart();
		}
	} catch (error) {
		console.error('Error changing language:', error);
	}
};

/**
 * Checks if the current language matches the preferred language
 * @param preferredLanguage The preferred language from API
 * @returns boolean indicating if language needs to be updated
 */
export const shouldUpdateLanguage = (preferredLanguage: string | undefined): boolean => {
	if (!preferredLanguage) return false;

	const currentLanguage = i18n.language;
	return currentLanguage !== preferredLanguage;
};

/**
 * Checks if a language requires RTL layout
 * @param language The language code
 * @returns boolean indicating if the language requires RTL
 */
export const isRTLLanguage = (language: string): boolean => {
	return language === 'ar';
};

/**
 * Checks if the RTL direction needs to be changed for a language
 * @param language The language code
 * @returns boolean indicating if RTL direction needs to be changed
 */
export const needsRTLChange = (language: string): boolean => {
	const isLangRTL = isRTLLanguage(language);
	return isLangRTL !== I18nManager.isRTL;
};
