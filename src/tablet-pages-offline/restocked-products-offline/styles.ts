import { PixelRatio, StyleSheet } from 'react-native';
import { HORIZONTAL_DIMENS, VERTICAL_DIMENS } from '../../constants';
import { colors, fonts } from '../../utils/theme';

export default StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: '#F6F6F6'
	},
	tabsContainer: {
		marginTop: 21,
		paddingHorizontal: 30
	},
	tabIconButton: {
		marginRight: 20
	},
	// listContainer: {
	// 	flexDirection: 'row',
	// 	flexWrap: 'wrap',
	// 	marginTop: 36,
	// 	paddingHorizontal: 30
	// },
	listContainer: {
		paddingVertical: 20,
		paddingHorizontal: 30
	},
	productCardContainer: {
		alignItems: 'center',
		width: '100%'
	},
	productCardSeparator: {
		height: PixelRatio.roundToNearestPixel(VERTICAL_DIMENS._20)
	},
	loadingContainer: {
		paddingTop: VERTICAL_DIMENS._24
	},
	noResults: {
		marginTop: VERTICAL_DIMENS._13,
		color: colors.grey400,
		fontFamily: fonts.Montserrat.Regular,
		fontWeight: '400',
		fontSize: HORIZONTAL_DIMENS._14,
		textAlign: 'center'
	},
	footerLoader: {
		height: 60,
		justifyContent: 'center',
		alignItems: 'center'
	},
	rightSpacing: {
		marginRight: 20
	}
});