/* eslint-disable react-native/no-inline-styles */
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import CodePush from 'react-native-code-push';
import { useTranslation } from 'react-i18next';
import notifee, { AuthorizationStatus } from '@notifee/react-native';
import BottomTabStack from './BottomTabStack';
import ChooseAccount from '../../../pages/choose-account';
import AddCustomer from '../../../mobile-pages/add-customer';
import CatalogVariant from '../../../mobile-pages/catalog-variant';
import ScanScreen from '../../../mobile-pages/scan';
import { setCodePushUpdate } from '../../../redux/features/common-slice';
import { useAppSelector } from '../../../redux/hooks';
import { colors } from '../../../utils/theme';
import { FakeStatusBar, HeaderMobile } from '../../../components/common';
import { Platform, TouchableOpacity, View } from 'react-native';
import Collection from '../../../mobile-pages/collection';
import Favorites from '../../../mobile-pages/favorites';
//import Recommended from '../../../mobile-pages/recommended';
import Language from '../../../mobile-pages/account/Language';
import Profile from '../../../mobile-pages/account/Profile';
import Orders from '../../../mobile-pages/account/Orders';
import Drafts from '../../../mobile-pages/account/Drafts';
import SearchResult from '../../../mobile-pages/search-result';
import Checkout from '../../../mobile-pages/checkout';
import TrackOrder from '../../../mobile-pages/account/TrackOrder';
import Customers from '../../../mobile-pages/account/Customers';
import UpdateCustomer from '../../../mobile-pages/update-customer';
import Notifications from '../../../mobile-pages/account/Notifications';
import NotificationSettings from '../../../mobile-pages/account/NotificationSettings';
import FilterResult from '../../../mobile-pages/filter-results';
import DraftDetails from '../../../mobile-pages/account/DraftDetails';
import NewProductsScreen from '../../../mobile-pages/new-products';
import RestockProductsScreen from '../../../mobile-pages/restock-products';
import Recommended from '../../../mobile-pages/recommended';
import Deals from '../../../mobile-pages/deals';
import CategoryProducts from '../../../mobile-pages/category-products';
import {
	ConfirmPayment,
	VerifyPayment,
	PaymentSuccess,
	CashAmount,
	PaymentCamera,
	TerminalAmount,
	TerminalReference,
	Transactions,
	TransactionDetails
} from '../../../mobile-pages/payment';
import { PhotoCapture } from '../../../components/mobile/tracking';
import { BottomSheetProvider } from '../../../components/common/BottomSheet';
import Settings from '../../../mobile-pages/account/Settings';
import Timesheet from '../../../mobile-pages/account/Timesheet';
import Campaign from '../../../mobile-pages/campaign';
import { PlusVector } from '../../../assets/svgs/icons';
import Statements from '../../../mobile-pages/statements';
import PdfViewer from '../../../mobile-pages/statements/PdfViewer';
import RewardProgram from '../../../mobile-pages/reward-program';
import HowItWorks from '../../../mobile-pages/reward-program/HowItWorks';
import RewardStore from '../../../mobile-pages/reward-program/RewardStore';
import RewardTransactions from '../../../mobile-pages/reward-program/RewardTransactions';
import NotificationCenter from '../../../mobile-pages/NotificationCenter';


const Stack = createNativeStackNavigator();

const AppStack = () => {
	const dispatch = useDispatch();
	const { t } = useTranslation();
	const isLoggedIn = useAppSelector((state) => state.auth.isLoggedIn);
	const userRoles = useAppSelector((state) => state.auth.userRoles);

	useEffect(() => {
		checkCodePushUpdate();
		reminderPermission();
	}, []);

	const reminderPermission = async () => {
		if (Platform.OS === 'ios') {
			const settings = await notifee.requestPermission();

			if (settings.authorizationStatus >= AuthorizationStatus.AUTHORIZED) {
				// console.log('Permission settings:', settings);
			} else {
				console.log('User declined permissions');
			}
		}
	};

	const checkCodePushUpdate = () => {
		// Check to see if there is still an update pending.
		CodePush.getUpdateMetadata(CodePush.UpdateState.PENDING).then((update) => {
			// console.log('update', update);
			if (update) {
				// There's a pending update, do we want to force a restart?
				dispatch(setCodePushUpdate(true));
			}
		});
	};

	const initialRouteName = !isLoggedIn && userRoles.length ? 'ChooseAccount' : 'AppStack';

	return (
		<BottomSheetProvider>
			<Stack.Navigator
				initialRouteName={initialRouteName}
				screenOptions={{
					headerStyle: {
						backgroundColor: colors.navbar
					},
					headerTintColor: colors.white,
					orientation: 'portrait'
				}}
			>
				<Stack.Screen
					name="AppStack"
					component={BottomTabStack}
					options={{
						headerShown: false,
						gestureEnabled: false
					}}
				/>
				<Stack.Screen
					name="ChooseAccount"
					component={ChooseAccount}
					options={{
						headerShown: false
					}}
				/>
				<Stack.Screen
					name="AddCustomer"
					component={AddCustomer}
					options={{
						headerBackTitle: '',
						header: () => <View>
							<FakeStatusBar />
							<HeaderMobile title={t('add_customer')} />
						</View>
					}}
				/>
				<Stack.Screen
					name="CatalogVariant"
					component={CatalogVariant}
					options={{
						headerBackTitle: '',
						headerShown: false,
						animation: 'none'
					}}
				/>
				<Stack.Screen
					name="NewProducts"
					component={NewProductsScreen}
					options={{
						headerBackTitle: '',
						header: () => <View>
							<FakeStatusBar />
							<HeaderMobile title={t('new_products')} />
						</View>
					}}
				/>
				<Stack.Screen
					name="RestockedProducts"
					component={RestockProductsScreen}
					options={{
						headerBackTitle: '',
						header: () => <View>
							<FakeStatusBar />
							<HeaderMobile title={t('restock_products')} />
						</View>
					}}
				/>
				<Stack.Screen
					name="Recommended"
					component={Recommended}
					options={{
						headerBackTitle: '',
						header: () => <View>
							<FakeStatusBar />
							<HeaderMobile title={t('recommended')} />
						</View>
					}}
				/>
				<Stack.Screen
					name="Collection"
					component={Collection}
					options={{
						headerBackTitle: '',
						header: () => <View>
							<FakeStatusBar />
							<HeaderMobile title={t('collection')} />
						</View>
					}}
				/>
				<Stack.Screen
					name="Deals"
					component={Deals}
					options={{
						headerBackTitle: '',
						header: () => <View>
							<FakeStatusBar />
							<HeaderMobile title={t('deals')} />
						</View>
					}}
				/>
				<Stack.Screen
					name="Favorites"
					component={Favorites}
					options={{
						headerBackTitle: '',
						header: () => <View>
							<FakeStatusBar />
							<HeaderMobile title={t('favorites')} />
						</View>
					}}
				/>
				<Stack.Screen
					name="Language"
					component={Language}
					options={{
						headerBackTitle: '',
						header: () => <View>
							<FakeStatusBar />
							<HeaderMobile title={t('language')} />
						</View>
					}}
				/>

				<Stack.Screen
					name="Profile"
					component={Profile}
					options={{
						headerBackTitle: '',
						header: () => <View>
							<FakeStatusBar />
							<HeaderMobile title={t('profile')} />
						</View>
					}}
				/>
				<Stack.Screen
					name="Customers"
					component={Customers}
					options={{
						headerBackTitle: '',
						// header: () => null
						header: ({ navigation }: any) => <View>
							<FakeStatusBar />
							<HeaderMobile
								title={t('customers')}
								headerRight={
									<TouchableOpacity
										style={{
											padding: 4,
											borderColor: colors.primary,
											borderWidth: 2,
											borderRadius: 20
										}}
										onPress={() => navigation.navigate('AddCustomer')}
									>
										<PlusVector height={12} width={12} fill={colors.primary} />
									</TouchableOpacity>
								}
							/>
						</View>
					}}
				/>
				<Stack.Screen
					name="UpdateCustomer"
					component={UpdateCustomer}
					options={{
						headerBackTitle: '',
						header: () => <View>
							<FakeStatusBar />
							<HeaderMobile title={t('update_customer')} />
						</View>
					}}
				/>
				<Stack.Screen
					name="Notifications"
					component={Notifications}
					options={{
						headerBackTitle: '',
						header: () => <View>
							<FakeStatusBar />
							<HeaderMobile title={t('notifications')} />
						</View>
					}}
				/>
				<Stack.Screen
					name="NotificationSettings"
					component={NotificationSettings}
					options={{
						headerBackTitle: '',
						header: () => <View>
							<FakeStatusBar />
							<HeaderMobile title={t('notifications')} />
						</View>
					}}
				/>
				<Stack.Screen
					name="Orders"
					component={Orders}
					options={{
						headerBackTitle: '',
						headerShown: false,
						animation: 'none'
					}}
				/>
				<Stack.Screen
					name="Drafts"
					component={Drafts}
					options={{
						headerBackTitle: '',
						header: () => <View>
							<FakeStatusBar />
							<HeaderMobile
								title={t('drafts')}
							/>
						</View>
					}}
				/>
				<Stack.Screen
					name="SearchResult"
					component={SearchResult}
					options={{
						headerBackTitle: '',
						header: () => <View>
							<FakeStatusBar />
						</View>,
						animation: 'none'
					}}
				/>
				<Stack.Screen
					name="FilterResult"
					component={FilterResult}
					options={{
						headerBackTitle: '',
						header: () => <View>
							<FakeStatusBar />
							<HeaderMobile title={t('results')} />
						</View>,
						animation: 'fade'
					}}
				/>
				<Stack.Screen
					name="Checkout"
					component={Checkout}
					options={{
						headerBackTitle: '',
						header: () => <View>
							<FakeStatusBar />
							<HeaderMobile title={t('checkout')} />
						</View>
					}}
				/>
				<Stack.Screen
					name="TrackOrder"
					component={TrackOrder}
					options={{
						headerBackTitle: t('track_order'),
						animation: 'none'
					}}
				/>
				<Stack.Screen
					name="Scanner"
					component={ScanScreen}
					options={{
						headerShown: false,
						animation: 'none'
					}}
				/>
				<Stack.Screen
					name="DraftDetail"
					component={DraftDetails}
					options={{
						headerBackTitle: '',
						header: () => <View>
							<FakeStatusBar />
							<HeaderMobile title={t('draft')} />
						</View>
					}}
				/>
				<Stack.Screen
					name="Settings"
					component={Settings}
					options={{
						headerBackTitle: '',
						header: () => <View>
							<FakeStatusBar />
							<HeaderMobile title={t('settings')} />
						</View>
					}}
				/>
				<Stack.Screen
					name="Timesheet"
					component={Timesheet}
					options={{
						headerBackTitle: '',
						header: () => <View>
							<FakeStatusBar />
							<HeaderMobile title={t('timesheet')} />
						</View>
					}}
				/>
				<Stack.Screen
					name="NotificationCenter"
					component={NotificationCenter}
					options={{
						headerBackTitle: '',
						header: () => <View>
							<FakeStatusBar />
							<HeaderMobile title={t('notification_center')} bottomLine />
						</View>
					}}
				/>
				<Stack.Screen
					name="PhotoCapture"
					component={PhotoCapture}
					options={{
						headerBackTitle: '',
						header: () => <View>
							<FakeStatusBar />
						</View>
					}}
				/>
				<Stack.Screen
					name="ConfirmPayment"
					component={ConfirmPayment}
					options={{
						headerBackTitle: ''
					}}
				/>
				<Stack.Screen
					name="VerifyPayment"
					component={VerifyPayment}
					options={{
						headerBackTitle: '',
						header: () => null
					}}
				/>
				<Stack.Screen
					name="PaymentSuccess"
					component={PaymentSuccess}
					options={{
						headerBackTitle: '',
						header: () => null,
						gestureEnabled: false
					}}
				/>
				<Stack.Screen
					name="CashAmount"
					component={CashAmount}
					options={{
						headerBackTitle: '',
						header: () => null,
						gestureDirection: 'vertical'
					}}
				/>
				<Stack.Screen
					name="PaymentCamera"
					component={PaymentCamera}
					options={{
						headerBackTitle: '',
						header: () => null,
						gestureDirection: 'vertical'
					}}
				/>
				<Stack.Screen
					name="TerminalAmount"
					component={TerminalAmount}
					options={{
						headerBackTitle: '',
						header: () => null
					}}
				/>
				<Stack.Screen
					name="TerminalReference"
					component={TerminalReference}
					options={{
						headerBackTitle: '',
						header: () => null
					}}
				/>
				<Stack.Screen
					name="Transactions"
					component={Transactions}
					options={{
						headerBackTitle: '',
						header: () => null,
						gestureDirection: 'vertical'
					}}
				/>
				<Stack.Screen
					name="TransactionDetails"
					component={TransactionDetails}
					options={{
						headerBackTitle: '',
						header: () => null,
						gestureDirection: 'vertical'
					}}
				/>
				<Stack.Screen
					name="Campaign"
					component={Campaign}
					options={{
						headerBackTitle: '',
						header: () => <View>
							<FakeStatusBar />
							<HeaderMobile title={'Promotion'} />
							{/* <HeaderMobile title={i18n.language !== 'en' ? 'اطب وربح' : 'Order to Win'} /> */}
						</View>
					}}
				/>
				<Stack.Screen
					name="CategoryProducts"
					component={CategoryProducts}
					options={{
						headerBackTitle: '',
						header: () => <View>
							<FakeStatusBar />
							<HeaderMobile title={'Philips'} />
						</View>
					}}
				/>
				<Stack.Screen
					name="Statements"
					component={Statements}
					options={{
						headerBackTitle: '',
						header: () => null
					}}
				/>
				<Stack.Screen
					name="PdfViewer"
					component={PdfViewer}
					options={{
						headerBackTitle: '',
						header: () => null
					}}
				/>
				<Stack.Screen
					name="RewardProgram"
					component={RewardProgram}
					options={{
						headerBackTitle: '',
						header: () => null,
						gestureDirection: 'vertical'
					}}
				/>
				<Stack.Screen
					name="RewardStore"
					component={RewardStore}
					options={{
						headerBackTitle: '',
						header: () => <View>
							<FakeStatusBar theme='light' />
							<HeaderMobile
								title={t('rewards_store')}
								bottomLine
							/>
						</View>
					}}
				/>
				<Stack.Screen
					name="RewardTransactions"
					component={RewardTransactions}
					options={{
						headerBackTitle: '',
						header: () => <View>
							<FakeStatusBar theme='light' />
							<HeaderMobile
								title={t('transactions')}
								bottomLine
							/>
						</View>
					}}
				/>
				<Stack.Screen
					name="HowItWorks"
					component={HowItWorks}
					options={{
						headerBackTitle: '',
						animation: 'ios',
						header: () => <View>
							<FakeStatusBar theme='light' />
							<HeaderMobile
								title={t('how_it_works')}
							/>
						</View>
					}}
				/>
			</Stack.Navigator>
		</BottomSheetProvider>
	);
};

export default AppStack;
